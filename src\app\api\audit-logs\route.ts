import { NextRequest } from 'next/server'

import {
  successResponse,
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  handleDatabaseError,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch audit logs with optional filtering
export const GET = withErrorHandler(async (request: NextRequest) => {
  const startTime = Date.now()
  console.log('🔄 Audit Logs API called')

  const { searchParams } = new URL(request.url)

  // Optional filters
  const table_name = searchParams.get('table')
  const operation = searchParams.get('operation')
  const search = searchParams.get('search')
  const dateFrom = searchParams.get('dateFrom')
  const dateTo = searchParams.get('dateTo')

  // Optional pagination - if not specified, return recent logs
  const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : null
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 100 // Default to 100 recent logs

  console.log('🔍 Filters:', { table_name, operation, search, dateFrom, dateTo, page, limit })

  try {
    let query = supabase
      .from('audit_log')
      .select('*', { count: 'exact' })
      .order('changed_at', { ascending: false })

    // Apply filters
    if (table_name) {
      query = query.eq('table_name', table_name)
    }

    if (operation) {
      query = query.eq('operation', operation)
    }

    if (search) {
      query = query.or(`changed_by.ilike.%${search}%,new_values->>name.ilike.%${search}%`)
    }

    if (dateFrom) {
      query = query.gte('changed_at', dateFrom)
    }

    if (dateTo) {
      query = query.lte('changed_at', dateTo)
    }

    // Apply pagination
    if (page && limit) {
      const offset = (page - 1) * limit
      query = query.range(offset, offset + limit - 1)
      console.log('📊 Using pagination:', { page, limit, offset })
    } else if (limit) {
      query = query.limit(limit)
      console.log('📊 Using limit:', { limit })
    }

    console.log('🔄 Executing audit log query...')

    const { data: logs, error, count } = await query

    if (error) {
      console.error('❌ Audit log query error:', error)
      throw handleDatabaseError(error)
    }

    const queryTime = Date.now() - startTime
    console.log('✅ Audit log query successful:', {
      logsCount: logs?.length || 0,
      totalCount: count,
      queryTime: `${queryTime}ms`
    })

    // Return response with or without pagination
    const responseData: any = {
      logs: logs || [],
    }

    if (page && limit) {
      responseData.pagination = {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    } else {
      responseData.total = count || 0
    }

    return successResponse(responseData)
  } catch (error) {
    const queryTime = Date.now() - startTime
    console.error('❌ Audit log query failed:', error, `after ${queryTime}ms`)
    throw error
  }
})

// POST - Create new audit log entry (for manual logging)
export const POST = withErrorHandler(async (request: NextRequest) => {
  const body = await request.json()

  // Validate required fields
  if (!body.table_name || !body.operation || !body.record_id) {
    return successResponse({ error: 'Missing required fields: table_name, operation, record_id' }, undefined, 400)
  }

  const {
    table_name,
    operation,
    record_id,
    old_values,
    new_values,
    changed_by
  } = body

  // Validate operation
  if (!['INSERT', 'UPDATE', 'DELETE'].includes(operation)) {
    return successResponse({ error: 'Invalid operation. Must be INSERT, UPDATE, or DELETE' }, undefined, 400)
  }

  const { data: log, error } = await supabase
    .from('audit_log')
    .insert([{
      table_name,
      operation,
      record_id,
      old_values: old_values || null,
      new_values: new_values || null,
      changed_by: changed_by || 'System'
    }])
    .select()
    .single()

  if (error) {
    console.error('❌ Audit log creation error:', error)
    throw handleDatabaseError(error)
  }

  return successResponse(log, 'Audit log entry created successfully', 201)
})
