import { NextRequest } from 'next/server'
import path from 'path'
import fs from 'fs'

import {
  successResponse,
  errorResponse,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validateRequestBody,
  validateRequiredFields,
  handleDatabaseError,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

// Fallback mock data function
function getMockProducts() {
  try {
    const mockDataPath = path.join(process.cwd(), 'src', 'data', 'mockProducts.json')
    const mockData = fs.readFileSync(mockDataPath, 'utf8')
    return JSON.parse(mockData)
  } catch (error) {
    console.warn('⚠️ Could not load mock data, using minimal fallback')
    return [
      {
        id: 1,
        name: 'Sample Product',
        price: 29.99,
        stock_quantity: 10,
        category: 'Electronics',
        description: 'Sample product for testing',
        created_at: new Date().toISOString()
      }
    ]
  }
}


// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch all products with optional filtering and performance optimizations
export const GET = withErrorHandler(async (request: NextRequest) => {
  const startTime = Date.now()
  console.log('🔄 Products API called')

  const { searchParams } = new URL(request.url)

  // Optional filters
  const category = searchParams.get('category')
  const search = searchParams.get('search')
  const lowStock = searchParams.get('lowStock') === 'true'

  // Optional pagination - if not specified, return ALL products
  const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : null
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : null

  console.log('🔍 Filters:', { category, search, lowStock, page, limit })

  try {
    let products, count

    // Try real database first
    try {
      console.log('🔄 Executing database query...')
      const { data, error: dbError } = await supabase
        .from('products')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false })

      if (dbError) throw dbError

      products = data || []
      count = data?.length || 0
      console.log(`✅ Query successful: { productsCount: ${products.length}, totalCount: ${count}, queryTime: '${Date.now() - startTime}ms' }`)
    } catch (dbError) {
      console.log('❌ Database error:', {
        message: (dbError as any)?.message || 'Unknown error',
        details: (dbError as any)?.stack || (dbError as any)?.toString?.() || String(dbError),
        hint: (dbError as any)?.hint || '',
        code: (dbError as any)?.code || ''
      })

      // Fallback to mock data only if database fails AND mock data is enabled
      const useMockData = process.env.USE_MOCK_DATA === 'true'
      if (useMockData) {
        console.log('🔄 Falling back to mock data due to database error')
        const mockProducts = getMockProducts()

        // Apply filters to mock data
        let filteredProducts = mockProducts

        if (category) {
          filteredProducts = filteredProducts.filter((p: any) => p.category === category)
        }

        if (search) {
          filteredProducts = filteredProducts.filter((p: any) =>
            p.name.toLowerCase().includes(search.toLowerCase())
          )
        }

        if (lowStock) {
          filteredProducts = filteredProducts.filter((p: any) => p.stock_quantity < 10)
        }

        // Apply pagination to mock data
        if (page && limit) {
          const offset = (page - 1) * limit
          products = filteredProducts.slice(offset, offset + limit)
          console.log('📊 Using pagination on mock data:', { page, limit, offset })
        } else {
          products = filteredProducts
          console.log('📊 No pagination - returning ALL mock products')
        }

        count = filteredProducts.length
        console.log(`✅ Mock data fallback successful: { productsCount: ${products.length}, totalCount: ${count}, queryTime: '${Date.now() - startTime}ms' }`)
      } else {
        // No fallback available, throw the database error
        console.log('❌ Database query failed:', {
          message: (dbError as any)?.message || 'Unknown error',
          details: (dbError as any)?.stack || (dbError as any)?.toString?.() || String(dbError),
          hint: (dbError as any)?.hint || '',
          code: (dbError as any)?.code || ''
        })

        error = dbError
        throw handleDatabaseError(dbError as Error & { code?: string })
      }
    }

    const queryTime = Date.now() - startTime
    console.log('✅ Query successful:', {
      productsCount: products?.length || 0,
      totalCount: count,
      queryTime: `${queryTime}ms`
    })

    // Return response with or without pagination
    const responseData: any = {
      products: products || [],
    }

    if (page && limit) {
      responseData.pagination = {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    } else {
      responseData.total = count || 0
    }

    return successResponse(responseData)
  } catch (error) {
    const queryTime = Date.now() - startTime
    console.error('❌ Database query failed:', error, `after ${queryTime}ms`)

    if (error instanceof Error && error.message === 'Database query timeout') {
      throw new Error('Database query timed out. Please try again or contact support if the issue persists.')
    }

    throw error
  }
})

// POST - Create new product
export const POST = withErrorHandler(async (request: NextRequest) => {
  const productData = await validateRequestBody(request, (body) => {
    // Type assertion for body to ensure it's an object
    const bodyData = body as Record<string, unknown>

    // Validate required fields
    validateRequiredFields(bodyData, ['name', 'net_weight', 'price', 'category'])

    return {
      name: String(bodyData.name).trim(),
      image_url: bodyData.image_url ? String(bodyData.image_url).trim() : null,
      image_public_id: bodyData.image_public_id ? String(bodyData.image_public_id).trim() : null,
      net_weight: String(bodyData.net_weight).trim(),
      price: parseFloat(bodyData.price as string),
      retail_price: bodyData.retail_price ? parseFloat(bodyData.retail_price as string) : null,
      stock_quantity: parseInt(bodyData.stock_quantity as string) || 0,
      category: String(bodyData.category).trim(),
    }
  })

  // Additional validation
  if (productData.price < 0) {
    return errorResponse('Unit price must be a positive number', 400)
  }

  if (productData.retail_price !== null && productData.retail_price < 0) {
    return errorResponse('Retail price must be a positive number', 400)
  }

  if (productData.stock_quantity < 0) {
    return errorResponse('Stock quantity must be a positive number', 400)
  }

  const { data: product, error } = await supabase
    .from('products')
    .insert([productData])
    .select()
    .single()

  if (error) {
    handleDatabaseError(error)
  }

  return successResponse(product, 'Product created successfully', 201)
})
