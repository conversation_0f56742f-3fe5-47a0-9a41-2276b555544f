import { NextRequest } from 'next/server'

import {
  successResponse,
  errorResponse,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validateRequestBody,
  handleDatabaseError
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

// GET - Fetch all settings or specific setting by key
export const GET = with<PERSON>rror<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const settingKey = searchParams.get('key')
  const category = searchParams.get('category')

  try {
    let query = supabase
      .from('settings')
      .select('*')
      .order('setting_category', { ascending: true })

    // Filter by specific key if provided
    if (settingKey) {
      query = query.eq('setting_key', settingKey)
    }

    // Filter by category if provided
    if (category) {
      query = query.eq('setting_category', category)
    }

    const { data: settings, error } = await query

    if (error) {
      return handleDatabaseError(error)
    }

    // If requesting a specific key, return just that setting's value
    if (settingKey && settings && settings.length > 0) {
      return successResponse({
        setting: settings[0],
        value: settings[0].setting_value
      })
    }

    // Group settings by category for easier frontend consumption
    const groupedSettings = settings?.reduce((acc, setting) => {
      const category = setting.setting_category
      if (!acc[category]) {
        acc[category] = {}
      }
      acc[category][setting.setting_key] = {
        value: setting.setting_value,
        description: setting.description,
        isPublic: setting.is_public,
        updatedAt: setting.updated_at
      }
      return acc
    }, {} as Record<string, any>) || {}

    return successResponse({
      settings: groupedSettings,
      raw: settings
    })
  } catch (error) {
    console.error('❌ Settings fetch error:', error)
    throw error
  }
})

// POST - Create or update setting
export const POST = withErrorHandler(async (request: NextRequest) => {
  const body = await validateRequestBody(request, (body) => {
    const bodyData = body as Record<string, unknown>
    
    if (!bodyData.setting_key || !bodyData.setting_value) {
      throw new Error('setting_key and setting_value are required')
    }
    
    return bodyData
  })

  const {
    setting_key,
    setting_value,
    setting_category = 'general',
    description = '',
    is_public = false
  } = body

  try {
    // Use upsert to create or update
    const { data: setting, error } = await supabase
      .from('settings')
      .upsert([
        {
          setting_key: String(setting_key).trim(),
          setting_value: setting_value,
          setting_category: String(setting_category).trim(),
          description: description?.toString().trim() || null,
          is_public: Boolean(is_public)
        }
      ], {
        onConflict: 'setting_key',
        ignoreDuplicates: false
      })
      .select()
      .single()

    if (error) {
      return handleDatabaseError(error)
    }

    return successResponse({
      message: 'Setting saved successfully',
      setting
    })
  } catch (error) {
    console.error('❌ Settings save error:', error)
    throw error
  }
})

// PUT - Update multiple settings at once
export const PUT = withErrorHandler(async (request: NextRequest) => {
  const body = await validateRequestBody(request, (body) => {
    const bodyData = body as Record<string, unknown>
    
    if (!bodyData.settings || !Array.isArray(bodyData.settings)) {
      throw new Error('settings array is required')
    }
    
    return bodyData
  })

  const { settings } = body

  try {
    // Prepare settings for upsert
    const settingsToUpsert = (settings as any[]).map((setting: any) => ({
      setting_key: setting.setting_key.toString().trim(),
      setting_value: setting.setting_value,
      setting_category: setting.setting_category?.toString().trim() || 'general',
      description: setting.description?.toString().trim() || null,
      is_public: Boolean(setting.is_public || false)
    }))

    const { data: updatedSettings, error } = await supabase
      .from('settings')
      .upsert(settingsToUpsert, {
        onConflict: 'setting_key',
        ignoreDuplicates: false
      })
      .select()

    if (error) {
      return handleDatabaseError(error)
    }

    return successResponse({
      message: `${updatedSettings?.length || 0} settings updated successfully`,
      settings: updatedSettings
    })
  } catch (error) {
    console.error('❌ Bulk settings update error:', error)
    throw error
  }
})

// DELETE - Delete a setting
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const settingKey = searchParams.get('key')

  if (!settingKey) {
    return errorResponse('setting_key parameter is required', 400)
  }

  try {
    const { error } = await supabase
      .from('settings')
      .delete()
      .eq('setting_key', settingKey)

    if (error) {
      return handleDatabaseError(error)
    }

    return successResponse({
      message: 'Setting deleted successfully'
    })
  } catch (error) {
    console.error('❌ Settings delete error:', error)
    throw error
  }
})
