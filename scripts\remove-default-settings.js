#!/usr/bin/env node

/**
 * Remove Default Settings Script
 * 
 * This script removes default store information from the database
 * to ensure all data comes from user input, not hardcoded defaults.
 * 
 * Professional approach: Database-first, no hardcoded defaults
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Remove default store information and replace with empty values
 */
async function removeDefaultSettings() {
  console.log('🧹 Removing default settings from database...');
  
  try {
    // Update store_info to have empty basic information
    const emptyStoreInfo = {
      name: '',
      address: '',
      phone: '',
      email: '',
      website: '',
      currency: 'PHP',
      timezone: 'Asia/Manila',
      businessHours: { open: '06:00', close: '22:00' },
      operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
      businessRegistration: {
        registrationNumber: '',
        taxId: '',
        businessType: 'Retail',
        registrationDate: ''
      },
      locations: [],
      branding: {
        logo: null,
        primaryColor: '#22c55e',
        secondaryColor: '#16a34a',
        slogan: ''
      }
    };

    const { data: storeUpdate, error: storeError } = await supabase
      .from('settings')
      .update({ 
        setting_value: emptyStoreInfo,
        updated_at: new Date().toISOString()
      })
      .eq('setting_key', 'store_info');

    if (storeError) {
      console.error('❌ Error updating store_info:', storeError);
      return false;
    }

    console.log('✅ Store information cleared - now database-driven only');

    // Also update profile settings to be empty
    const emptyProfileInfo = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      role: '',
      avatar: null,
      bio: '',
      dateOfBirth: '',
      address: '',
      emergencyContact: {
        name: '',
        phone: '',
        relationship: ''
      },
      preferences: {
        language: 'en',
        timezone: 'Asia/Manila',
        dateFormat: 'MM/DD/YYYY',
        numberFormat: 'en-US'
      }
    };

    const { data: profileUpdate, error: profileError } = await supabase
      .from('settings')
      .update({ 
        setting_value: emptyProfileInfo,
        updated_at: new Date().toISOString()
      })
      .eq('setting_key', 'profile_settings');

    if (profileError) {
      console.error('❌ Error updating profile_settings:', profileError);
      return false;
    }

    console.log('✅ Profile information cleared - now database-driven only');

    // Update notification channels to be empty
    const { data: currentNotifications, error: fetchError } = await supabase
      .from('settings')
      .select('setting_value')
      .eq('setting_key', 'notification_settings')
      .single();

    if (!fetchError && currentNotifications) {
      const updatedNotifications = {
        ...currentNotifications.setting_value,
        channels: {
          email: '',
          sms: '',
          webhook: ''
        }
      };

      const { data: notificationUpdate, error: notificationError } = await supabase
        .from('settings')
        .update({ 
          setting_value: updatedNotifications,
          updated_at: new Date().toISOString()
        })
        .eq('setting_key', 'notification_settings');

      if (notificationError) {
        console.error('❌ Error updating notification_settings:', notificationError);
        return false;
      }

      console.log('✅ Notification channels cleared - now database-driven only');
    }

    return true;
  } catch (error) {
    console.error('❌ Error removing default settings:', error);
    return false;
  }
}

/**
 * Verify the changes
 */
async function verifyChanges() {
  console.log('🔍 Verifying changes...');
  
  try {
    const { data: settings, error } = await supabase
      .from('settings')
      .select('setting_key, setting_value')
      .in('setting_key', ['store_info', 'profile_settings', 'notification_settings']);

    if (error) {
      console.error('❌ Error verifying changes:', error);
      return false;
    }

    settings.forEach(setting => {
      console.log(`📋 ${setting.setting_key}:`);
      if (setting.setting_key === 'store_info') {
        const store = setting.setting_value;
        console.log(`   - Name: "${store.name}" (should be empty)`);
        console.log(`   - Address: "${store.address}" (should be empty)`);
        console.log(`   - Phone: "${store.phone}" (should be empty)`);
        console.log(`   - Email: "${store.email}" (should be empty)`);
      } else if (setting.setting_key === 'profile_settings') {
        const profile = setting.setting_value;
        console.log(`   - First Name: "${profile.firstName}" (should be empty)`);
        console.log(`   - Last Name: "${profile.lastName}" (should be empty)`);
        console.log(`   - Email: "${profile.email}" (should be empty)`);
      } else if (setting.setting_key === 'notification_settings') {
        const notifications = setting.setting_value;
        console.log(`   - Email Channel: "${notifications.channels?.email || ''}" (should be empty)`);
        console.log(`   - SMS Channel: "${notifications.channels?.sms || ''}" (should be empty)`);
      }
    });

    return true;
  } catch (error) {
    console.error('❌ Error verifying changes:', error);
    return false;
  }
}

/**
 * Main execution
 */
async function main() {
  console.log('🚀 Starting default settings removal...');
  console.log('📋 This will make the app purely database-driven for Basic Information');
  console.log('');

  const success = await removeDefaultSettings();
  
  if (success) {
    console.log('');
    await verifyChanges();
    console.log('');
    console.log('✅ Default settings successfully removed!');
    console.log('📋 Basic Information is now purely database-driven');
    console.log('👤 Users must enter their own store information');
  } else {
    console.log('❌ Failed to remove default settings');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { removeDefaultSettings, verifyChanges };
