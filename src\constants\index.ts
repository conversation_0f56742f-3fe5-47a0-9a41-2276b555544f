// Application constants and configuration

// Application metadata
export const APP_CONFIG = {
  name: 'Revantad Store',
  description: 'Professional admin dashboard for sari-sari store management',
  version: '1.0.0',
  author: 'Revantad Store Team',
} as const

// API endpoints
export const API_ENDPOINTS = {
  products: '/api/products',
  customers: '/api/customers',
  debts: '/api/debts',
  payments: '/api/payments',
  settings: '/api/settings',
  upload: '/api/upload',
} as const

// Product categories
export const PRODUCT_CATEGORIES = [
  'Instant Foods',
  'Beverages',
  'Canned Goods',
  'Personal Care',
  'Rice & Grains',
  'Condiments',
  'Household Items',
  'Snacks',
  'Dairy Products',
  'Frozen Foods',
  'Cleaning Supplies',
  'School Supplies',
  'Others'
] as const

// Default pagination settings
export const PAGINATION_DEFAULTS = {
  page: 1,
  limit: 10,
  maxLimit: 100,
} as const

// Theme configuration
export const THEME_CONFIG = {
  defaultTheme: 'light',
  storageKey: 'revantad-theme',
  themes: ['light', 'dark'],
} as const

// Form validation rules
export const VALIDATION_RULES = {
  product: {
    name: { minLength: 2, maxLength: 255 },
    price: { min: 0, max: 999999.99 },
    stock: { min: 0, max: 999999 },
    netWeight: { minLength: 1, maxLength: 100 },
  },
  customer: {
    customerName: { minLength: 2, maxLength: 255 },
    familyName: { minLength: 2, maxLength: 255 },
    phoneNumber: { minLength: 10, maxLength: 20 },
  },
  debt: {
    customerName: { minLength: 2, maxLength: 255 },
    familyName: { minLength: 2, maxLength: 255 },
    productName: { minLength: 2, maxLength: 255 },
    productPrice: { min: 0.01, max: 999999.99 },
    quantity: { min: 1, max: 999999 },
  },
  payment: {
    paymentAmount: { min: 0.01, max: 999999.99 },
    responsibleMember: { minLength: 2, maxLength: 255 },
  },
} as const

// UI constants
export const UI_CONSTANTS = {
  headerHeight: '64px',
  sidebarWidth: '256px',
  sidebarCollapsedWidth: '64px',
  maxContentWidth: '1200px',
  borderRadius: {
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px',
  },
  zIndex: {
    dropdown: 1000,
    modal: 1050,
    tooltip: 1100,
    toast: 1200,
  },
} as const

// Animation durations (in milliseconds)
export const ANIMATION_DURATION = {
  fast: 150,
  normal: 300,
  slow: 500,
} as const

// Local storage keys
export const STORAGE_KEYS = {
  user: 'revantad_user',
  theme: 'revantad-theme',
  sidebarCollapsed: 'revantad_sidebar_collapsed',
  recentSearches: 'revantad_recent_searches',
} as const

// Error messages
export const ERROR_MESSAGES = {
  generic: 'Something went wrong. Please try again.',
  network: 'Network error. Please check your connection.',
  unauthorized: 'You are not authorized to perform this action.',
  notFound: 'The requested resource was not found.',
  validation: 'Please check your input and try again.',
  upload: 'Failed to upload file. Please try again.',
} as const

// Success messages
export const SUCCESS_MESSAGES = {
  created: 'Successfully created!',
  updated: 'Successfully updated!',
  deleted: 'Successfully deleted!',
  uploaded: 'File uploaded successfully!',
  saved: 'Changes saved successfully!',
} as const

// Date formats
export const DATE_FORMATS = {
  display: 'MMM dd, yyyy',
  input: 'yyyy-MM-dd',
  timestamp: 'yyyy-MM-dd HH:mm:ss',
  relative: 'relative',
} as const

// File upload constraints
export const UPLOAD_CONSTRAINTS = {
  maxFileSize: 5 * 1024 * 1024, // 5MB
  allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
  allowedDocumentTypes: ['application/pdf', 'text/plain'],
} as const

// Default login credentials (for development)
export const DEFAULT_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123',
} as const
