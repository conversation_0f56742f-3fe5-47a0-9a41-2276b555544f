#!/usr/bin/env node

/**
 * Update Database Schema for Settings Table
 * This script adds the settings table and related components to the existing database
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  console.error('   NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅ Set' : '❌ Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// SQL commands to add settings table
const settingsTableSQL = `
-- Create settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    setting_key VARCHAR(255) NOT NULL UNIQUE,
    setting_value JSONB NOT NULL,
    setting_category VARCHAR(100) NOT NULL DEFAULT 'general',
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT settings_key_not_empty CHECK (LENGTH(TRIM(setting_key)) > 0),
    CONSTRAINT settings_category_not_empty CHECK (LENGTH(TRIM(setting_category)) > 0),
    CONSTRAINT settings_valid_category CHECK (setting_category IN ('store', 'profile', 'notifications', 'security', 'appearance', 'backup', 'general'))
);
`;

const settingsIndexesSQL = `
-- Create indexes for settings table
CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_settings_category ON settings(setting_category);
CREATE INDEX IF NOT EXISTS idx_settings_public ON settings(is_public);
CREATE INDEX IF NOT EXISTS idx_settings_category_key ON settings(setting_category, setting_key);
CREATE INDEX IF NOT EXISTS idx_settings_created_at ON settings(created_at);
`;

const settingsTriggersSQL = `
-- Add timestamp trigger for settings table
CREATE TRIGGER IF NOT EXISTS update_settings_updated_at
    BEFORE UPDATE ON settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
`;

const settingsRLSSQL = `
-- Enable RLS and create policy for settings table
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Enable all operations for application" ON settings;
CREATE POLICY "Enable all operations for application" ON settings FOR ALL USING (true);
`;

const settingsSampleDataSQL = `
-- Insert default settings if table is empty
INSERT INTO settings (setting_key, setting_value, setting_category, description, is_public)
SELECT * FROM (VALUES
('store_info', '{"name":"Revantad Store","address":"123 Barangay Street, Manila, Philippines","phone":"+63 ************","email":"<EMAIL>","website":"https://revantadstore.com","currency":"PHP","timezone":"Asia/Manila","businessHours":{"open":"06:00","close":"22:00"},"operatingDays":["monday","tuesday","wednesday","thursday","friday","saturday"],"businessRegistration":{"registrationNumber":"REG-2024-001","taxId":"TAX-*********","businessType":"Retail","registrationDate":"2024-01-01"},"locations":[{"id":1,"name":"Main Store","address":"123 Barangay Street, Manila, Philippines","phone":"+63 ************","isMain":true}],"branding":{"logo":null,"primaryColor":"#22c55e","secondaryColor":"#16a34a","slogan":"Your Neighborhood Store"}}', 'store', 'Store information and business details', true),
('appearance_settings', '{"theme":"light","language":"en","dateFormat":"MM/dd/yyyy","numberFormat":"en-US","colorScheme":{"primary":"#22c55e","secondary":"#16a34a","accent":"#10b981","background":"#ffffff","surface":"#f8fafc"},"layout":{"sidebarPosition":"left","density":"comfortable","showAnimations":true,"compactMode":false},"typography":{"fontFamily":"Inter","fontSize":"medium","fontWeight":"normal"}}', 'appearance', 'UI appearance and theme settings', true),
('notification_settings', '{"lowStock":true,"newDebt":true,"paymentReceived":true,"dailyReport":false,"weeklyReport":true,"emailNotifications":true,"smsNotifications":false,"pushNotifications":true,"channels":{"email":"<EMAIL>","sms":"","webhook":""},"templates":{"lowStock":"Product {{product_name}} is running low ({{quantity}} remaining)","newDebt":"New debt recorded for {{customer_name}}: PHP {{amount}}","paymentReceived":"Payment received from {{customer_name}}: PHP {{amount}}"}}', 'notifications', 'Notification preferences and templates', false),
('security_settings', '{"twoFactorAuth":false,"sessionTimeout":"24","passwordExpiry":"90","loginAttempts":"5"}', 'security', 'Security and authentication settings', false),
('backup_settings', '{"autoBackup":true,"backupFrequency":"daily","retentionDays":"30","lastBackup":"","cloudStorage":{"provider":"","bucket":"","accessKey":"","secretKey":""},"verification":{"enabled":true,"lastVerified":"","status":"pending"}}', 'backup', 'Backup and data retention settings', false)
) AS sample_settings(setting_key, setting_value, setting_category, description, is_public)
WHERE NOT EXISTS (SELECT 1 FROM settings LIMIT 1);
`;

async function updateSchema() {
  console.log('🚀 Starting Settings Schema Update...\n');
  console.log('⚠️  Note: This script will test the database connection and insert default settings.');
  console.log('⚠️  For full schema updates, please run the SQL from database/tindahan_unified_schema.sql');
  console.log('⚠️  in your Supabase dashboard SQL Editor.\n');

  try {
    // Test database connection
    console.log('📋 Step 1: Testing database connection...');
    const { data: testData, error: testError } = await supabase
      .from('products')
      .select('count(*)', { count: 'exact', head: true });

    if (testError) {
      console.error('❌ Database connection failed:', testError);
      return;
    }
    console.log('✅ Database connection successful');

    // Check if settings table exists
    console.log('📋 Step 2: Checking if settings table exists...');
    const { data: settingsCheck, error: checkError } = await supabase
      .from('settings')
      .select('count(*)', { count: 'exact', head: true });

    if (checkError) {
      console.log('⚠️  Settings table does not exist yet');
      console.log('📋 Please run the updated database/tindahan_unified_schema.sql in Supabase SQL Editor');
      console.log('🔗 Go to: https://supabase.com/dashboard/project/[your-project]/sql');
      return;
    }
    console.log('✅ Settings table exists');

    // Insert default settings if table is empty
    console.log('📋 Step 3: Checking for existing settings...');
    const { data: existingSettings, error: existingError } = await supabase
      .from('settings')
      .select('setting_key')
      .limit(1);

    if (existingError) {
      console.error('❌ Error checking existing settings:', existingError);
      return;
    }

    if (existingSettings && existingSettings.length > 0) {
      console.log('✅ Settings already exist, skipping default data insertion');
    } else {
      console.log('📋 Step 4: Inserting default settings...');

      const defaultSettings = [
        {
          setting_key: 'store_info',
          setting_value: {
            name: 'Revantad Store',
            address: '123 Barangay Street, Manila, Philippines',
            phone: '+63 ************',
            email: '<EMAIL>',
            website: 'https://revantadstore.com',
            currency: 'PHP',
            timezone: 'Asia/Manila',
            businessHours: { open: '06:00', close: '22:00' },
            operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
            businessRegistration: {
              registrationNumber: 'REG-2024-001',
              taxId: 'TAX-*********',
              businessType: 'Retail',
              registrationDate: '2024-01-01'
            },
            locations: [{
              id: 1,
              name: 'Main Store',
              address: '123 Barangay Street, Manila, Philippines',
              phone: '+63 ************',
              isMain: true
            }],
            branding: {
              logo: null,
              primaryColor: '#22c55e',
              secondaryColor: '#16a34a',
              slogan: 'Your Neighborhood Store'
            }
          },
          setting_category: 'store',
          description: 'Store information and business details',
          is_public: true
        },
        {
          setting_key: 'appearance_settings',
          setting_value: {
            theme: 'light',
            language: 'en',
            dateFormat: 'MM/dd/yyyy',
            numberFormat: 'en-US',
            colorScheme: {
              primary: '#22c55e',
              secondary: '#16a34a',
              accent: '#10b981',
              background: '#ffffff',
              surface: '#f8fafc'
            },
            layout: {
              sidebarPosition: 'left',
              density: 'comfortable',
              showAnimations: true,
              compactMode: false
            },
            typography: {
              fontFamily: 'Inter',
              fontSize: 'medium',
              fontWeight: 'normal'
            }
          },
          setting_category: 'appearance',
          description: 'UI appearance and theme settings',
          is_public: true
        }
      ];

      const { error: insertError } = await supabase
        .from('settings')
        .insert(defaultSettings);

      if (insertError) {
        console.error('❌ Error inserting default settings:', insertError);
        return;
      }
      console.log('✅ Default settings inserted successfully');
    }

    // Step 6: Verify the setup
    console.log('📋 Step 6: Verifying setup...');
    const { data: settings, error: verifyError } = await supabase
      .from('settings')
      .select('setting_key, setting_category, created_at')
      .order('setting_category');

    if (verifyError) {
      console.error('❌ Error verifying setup:', verifyError);
      return;
    }

    console.log('✅ Setup verification successful!');
    console.log('📊 Settings found:', settings?.length || 0);
    if (settings && settings.length > 0) {
      console.log('📋 Settings categories:');
      settings.forEach(setting => {
        console.log(`   • ${setting.setting_key} (${setting.setting_category})`);
      });
    }

    console.log('\n🎉 Settings schema update completed successfully!');
    console.log('🔧 The Settings page is now connected to the Supabase database.');
    console.log('💾 Settings will be synchronized across all instances.');

  } catch (error) {
    console.error('❌ Unexpected error during schema update:', error);
  }
}

// Run the update
updateSchema();
